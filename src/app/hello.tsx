import { StyleSheet, <PERSON><PERSON>, Button } from 'react-native';
import { useNavigation } from 'expo-router';
import { useLayoutEffect } from 'react';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

export default function HelloScreen() {
	const navigation = useNavigation();

	useLayoutEffect(() => {
		navigation.setOptions({
			headerRight: () => (
				<Button onPress={() => Alert.alert('Button pressed!')} title='Click Me' />
			),
		});
	}, [navigation]);

	return (
		<ThemedView style={styles.titleContainer}>
			<ThemedText type='title'>Hello Navigation</ThemedText>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	titleContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		gap: 8,
		marginTop: 100,
	},
	stepContainer: {
		gap: 8,
		marginBottom: 8,
	},
});
